import json
import uuid
import logging
import traceback
import inspect
import threading
from datetime import datetime
from django.utils import timezone

logger = logging.getLogger(__name__)

# Thread-local storage for request context
_thread_local = threading.local()


def get_client_ip(request):
    """
    Get client IP address from request
    """
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0].strip()
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def generate_unique_id():
    """
    Generate a unique identifier for logging correlation.
    Format: API_<full_uuid>

    This is the single function for generating unique IDs throughout the application.
    Replaces both generate_unique_request_id() and generate_api_call_uuid().
    """
    return f"API_{str(uuid.uuid4())}"


# Backward compatibility aliases
def generate_api_call_uuid():
    """Alias for generate_unique_id() - for backward compatibility."""
    return generate_unique_id()


def set_request_context(unique_id=None, **context):
    """
    Set the request context for the current thread.

    Args:
        unique_id (str): Unique identifier for the request (optional)
        **context: Additional context data
    """
    if unique_id:
        _thread_local.unique_id = unique_id
    _thread_local.context = context


def get_current_unique_id():
    """
    Get the current unique ID from thread-local storage.
    If no ID is set, try to get it from request context, otherwise generate a new one.

    Returns:
        str: Current unique ID for the thread
    """
    # First check if unique_id is directly stored in thread-local
    if hasattr(_thread_local, "unique_id") and _thread_local.unique_id:
        return _thread_local.unique_id

    # If not found, try to get it from the context (middleware might have stored it there)
    context = getattr(_thread_local, "context", {})
    if "unique_id" in context:
        _thread_local.unique_id = context["unique_id"]
        return _thread_local.unique_id

    # If still not found, generate a new one
    _thread_local.unique_id = generate_unique_id()
    return _thread_local.unique_id


def get_request_context():
    """
    Get the current request context from thread-local storage.

    Returns:
        dict: Current context data
    """
    return getattr(_thread_local, "context", {})


def clear_request_context():
    """
    Clear the request context for the current thread.
    """
    if hasattr(_thread_local, "unique_id"):
        delattr(_thread_local, "unique_id")
    if hasattr(_thread_local, "context"):
        delattr(_thread_local, "context")


class LoggingContext:
    """
    Context manager for setting logging context with automatic cleanup.

    Usage:
        with LoggingContext(operation="user_registration", user_id=123):
            enhanced_log("Starting user registration")
            # All logs in this block will share the same unique ID
            some_function()  # This function's logs will also use the same ID
    """

    def __init__(self, **context):
        self.context = context
        self.previous_unique_id = None
        self.previous_context = None

    def __enter__(self):
        # Save previous context
        self.previous_context = getattr(_thread_local, "context", {})

        # Set new context
        set_request_context(**self.context)
        return get_current_unique_id()

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore previous context
        _thread_local.context = self.previous_context


def get_caller_info():
    """
    Get information about the caller (file name, line number, function name).
    Returns a dictionary with caller details.
    """
    try:
        # Get the current frame and go up the stack to find the actual caller
        frame = inspect.currentframe()
        caller_frame = (
            frame.f_back.f_back
        )  # Go up two levels to skip this function and the logging function

        if caller_frame:
            filename = caller_frame.f_code.co_filename.split("/")[
                -1
            ]  # Get just the filename
            line_number = caller_frame.f_lineno
            function_name = caller_frame.f_code.co_name

            return {
                "filename": filename,
                "line_number": line_number,
                "function_name": function_name,
            }
    except Exception:
        pass

    return {"filename": "unknown", "line_number": 0, "function_name": "unknown"}


def enhanced_log(
    message,
    level="INFO",
    operation_type="GENERAL",
    metadata=None,
    exception=None,
):
    """
    Enhanced logging function with automatic file name, line number, and unique ID.
    Uses thread-local storage to automatically track unique ID across function calls.

    Args:
        message (str): Log message
        level (str): Log level (INFO, WARNING, ERROR)
        operation_type (str): Type of operation being logged
        metadata (dict): Additional metadata
        exception (Exception): Exception object for error logging

    Returns:
        str: The unique_id used for logging
    """

    # Merge thread-local context with provided metadata
    combined_metadata = get_request_context().copy()
    if metadata:
        combined_metadata.update(metadata)

    if level == "ERROR" and exception:
        log_error_with_traceback(
            operation_type=operation_type,
            message=message,
            exception=exception,
            metadata=combined_metadata,
        )
    else:
        log_operation_info(
            operation_type=operation_type,
            message=message,
            metadata=combined_metadata,
            level=level,
            include_caller_info=True,
        )


def log_request_info(request, operation_type="REQUEST"):
    """
    Logs request information in a standardized JSON format with unique id, headers, data, and meta.
    Output format: INFO | <operation_type> | <unique_id> | <headers> | <data>
    Uses thread-local storage to automatically get the unique ID.

    Args:
        request: Django request object
        operation_type (str): Type of operation (REQUEST, RESPONSE, ERROR, etc.)
    """
    try:
        # Extract headers safely
        headers = {}
        if hasattr(request, "headers"):
            headers = dict(request.headers)
        elif hasattr(request, "META"):
            # Extract HTTP headers from META
            headers = {k: v for k, v in request.META.items() if k.startswith("HTTP_")}

        # Extract request data safely
        data = {}
        if hasattr(request, "data"):
            data = request.data
        elif hasattr(request, "POST"):
            data = dict(request.POST)

        # Create log entry
        log_data = {
            "timestamp": timezone.now().isoformat(),
            "method": getattr(request, "method", "UNKNOWN"),
            "path": getattr(request, "path", ""),
            "headers": headers,
            "data": data,
            "client_ip": get_client_ip(request),
            "user_agent": (
                request.META.get("HTTP_USER_AGENT", "")
                if hasattr(request, "META")
                else ""
            ),
        }

        # Convert to JSON string
        headers_json = json.dumps(headers, default=str, separators=(",", ":"))
        data_json = json.dumps(data, default=str, separators=(",", ":"))

        # Log in the standardized format with UUID
        unique_id = get_current_unique_id()
        logger.info(
            f"INFO | {operation_type} | {unique_id} | {headers_json} | {data_json}"
        )

    except Exception as e:
        # Fallback logging if there's an error
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | LOGGING_FAILURE | {unique_id} | {error_log}")


def log_response_info(response_data, status_code=200, operation_type="RESPONSE"):
    """
    Logs response information in a standardized JSON format.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        response_data: Response data to log
        status_code (int): HTTP status code
        operation_type (str): Type of operation (RESPONSE, SUCCESS, ERROR, etc.)
    """
    try:
        log_data = {
            "timestamp": timezone.now().isoformat(),
            "status_code": status_code,
            "response_data": response_data,
        }

        response_json = json.dumps(log_data, default=str, separators=(",", ":"))
        unique_id = get_current_unique_id()
        logger.info(f"INFO | {operation_type} | {unique_id} | {response_json}")

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | LOGGING_FAILURE | {unique_id} | {error_log}")


def log_operation_info(
    operation_type,
    message,
    metadata=None,
    level="INFO",
    include_caller_info=True,
):
    """
    Logs operation information in a standardized format with file name and line number.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        operation_type (str): Type of operation (USER_CREATION, OAUTH_APP_CREATION, etc.)
        message (str): Descriptive message
        metadata (dict): Additional metadata to include
        level (str): Log level (INFO, WARNING, ERROR)
        include_caller_info (bool): Whether to include caller file/line info
    """
    try:
        # Get caller information for better debugging
        caller_info = get_caller_info() if include_caller_info else {}

        log_data = {
            "timestamp": timezone.now().isoformat(),
            "operation": operation_type,
            "message": message,
            "metadata": metadata or {},
        }

        # Add caller information to metadata if available
        if caller_info and caller_info.get("filename") != "unknown":
            log_data["caller"] = caller_info

        log_json = json.dumps(log_data, default=str, separators=(",", ":"))
        unique_id = get_current_unique_id()

        # Format log message with caller info for errors
        if level == "ERROR" and caller_info.get("filename") != "unknown":
            log_message = f"ERROR | {operation_type} | {caller_info['filename']}:{caller_info['line_number']} | {unique_id} | {log_json}"
            logger.error(log_message)
        elif level == "WARNING":
            log_message = f"WARNING | {operation_type} | {unique_id} | {log_json}"
            logger.warning(log_message)
        else:
            log_message = f"INFO | {operation_type} | {unique_id} | {log_json}"
            logger.info(log_message)

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | LOGGING_FAILURE | {unique_id} | {error_log}")


def log_error_with_traceback(operation_type, message, exception=None, metadata=None):
    """
    Logs error information with full traceback, file name, and line number.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        operation_type (str): Type of operation where error occurred
        message (str): Descriptive error message
        exception (Exception): The exception object (optional)
        metadata (dict): Additional metadata to include
    """
    try:
        # Get caller information
        caller_info = get_caller_info()

        # Get traceback information
        tb_info = None
        if exception:
            tb_info = {
                "exception_type": exception.__class__.__name__,
                "exception_message": str(exception),
                "traceback": traceback.format_exc(),
            }

        log_data = {
            "timestamp": timezone.now().isoformat(),
            "operation": operation_type,
            "message": message,
            "metadata": metadata or {},
            "caller": caller_info,
            "error_details": tb_info,
        }

        log_json = json.dumps(log_data, default=str, separators=(",", ":"))
        unique_id = get_current_unique_id()

        # Format error message with file and line info
        if caller_info.get("filename") != "unknown":
            log_message = f"ERROR | {operation_type} | {caller_info['filename']}:{caller_info['line_number']} | {unique_id} | {log_json}"
        else:
            log_message = f"ERROR | {operation_type} | {unique_id} | {log_json}"

        logger.error(log_message)

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | ERROR_LOGGING_FAILURE | {unique_id} | {error_log}")


def log_security_event_standardized(
    event_type,
    description,
    user=None,
    request=None,
    metadata=None,
    level="WARNING",
):
    """
    Log security events in a standardized format.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        event_type (str): Type of security event (LOGIN_FAILURE, SUSPICIOUS_ACTIVITY, etc.)
        description (str): Description of the security event
        user: User object (optional)
        request: Django request object (optional)
        metadata (dict): Additional metadata
        level (str): Log level (INFO, WARNING, ERROR)
    """
    try:
        security_metadata = metadata or {}

        # Add user information if available
        if user:
            security_metadata.update(
                {
                    "user_id": getattr(user, "id", None),
                    "user_email": getattr(user, "email", None),
                    "user_role": getattr(user, "role", None),
                }
            )

        # Add request information if available
        if request:
            security_metadata.update(
                {
                    "client_ip": get_client_ip(request),
                    "user_agent": (
                        request.META.get("HTTP_USER_AGENT", "")
                        if hasattr(request, "META")
                        else ""
                    ),
                    "request_method": getattr(request, "method", "UNKNOWN"),
                    "request_path": getattr(request, "path", ""),
                }
            )

        log_operation_info(
            operation_type=f"SECURITY_{event_type}",
            message=description,
            metadata=security_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | SECURITY_LOGGING_FAILURE | {unique_id} | {error_log}")


def log_database_operation(
    operation_type,
    table_name,
    operation_result=None,
    metadata=None,
    level="INFO",
):
    """
    Log database operations in a standardized format.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        operation_type (str): Type of database operation (CREATE, UPDATE, DELETE, SELECT)
        table_name (str): Name of the database table/model
        operation_result (str): Result of the operation (SUCCESS, FAILURE, etc.)
        metadata (dict): Additional metadata (record_id, affected_rows, etc.)
        level (str): Log level
    """
    try:
        db_metadata = metadata or {}
        db_metadata.update(
            {
                "table_name": table_name,
                "operation_result": operation_result or "UNKNOWN",
            }
        )

        log_operation_info(
            operation_type=f"DB_{operation_type}",
            message=f"Database {operation_type.lower()} operation on {table_name}",
            metadata=db_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | DB_LOGGING_FAILURE | {unique_id} | {error_log}")


def log_api_call(
    api_name=None,
    endpoint=None,
    method="GET",
    status_code=None,
    response_time=None,
    metadata=None,
    level="INFO",
):
    """
    Log external API calls in a standardized format with file name and line number.
    Uses thread-local storage to automatically track unique ID across function calls.

    Args:
        api_name (str): Name of the external API
        endpoint (str): API endpoint called
        method (str): HTTP method used
        status_code (int): HTTP status code received
        response_time (float): Response time in seconds
        metadata (dict): Additional metadata
        level (str): Log level
    """
    try:
        # Use thread-local unique ID if not provided

        # Get caller information
        caller_info = get_caller_info()

        # Merge thread-local context with provided metadata
        api_metadata = get_request_context().copy()
        if metadata:
            api_metadata.update(metadata)

        api_metadata.update(
            {
                "api_name": api_name,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time_seconds": response_time,
                "caller": caller_info,
            }
        )

        message = f"API call to {api_name} - {method} {endpoint}"
        if status_code:
            message += f" - Status: {status_code}"
        if response_time:
            message += f" - Time: {response_time:.3f}s"

        log_operation_info(
            operation_type="API_CALL",
            message=message,
            metadata=api_metadata,
            level=level,
            include_caller_info=True,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | API_LOGGING_FAILURE | {unique_id} | {error_log}")


def log_business_event(
    event_type,
    description,
    entity_type=None,
    entity_id=None,
    metadata=None,
    level="INFO",
):
    """
    Log business events in a standardized format.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        event_type (str): Type of business event (ORDER_CREATED, PAYMENT_PROCESSED, etc.)
        description (str): Description of the business event
        entity_type (str): Type of business entity (ORDER, PAYMENT, USER, etc.)
        entity_id (str): ID of the business entity
        metadata (dict): Additional metadata
        level (str): Log level
    """
    try:
        business_metadata = metadata or {}
        if entity_type:
            business_metadata["entity_type"] = entity_type
        if entity_id:
            business_metadata["entity_id"] = entity_id

        log_operation_info(
            operation_type=f"BUSINESS_{event_type}",
            message=description,
            metadata=business_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | BUSINESS_LOGGING_FAILURE | {unique_id} | {error_log}")


def log_performance_metric(
    metric_name,
    metric_value,
    unit="ms",
    operation_type=None,
    metadata=None,
    level="INFO",
):
    """
    Log performance metrics in a standardized format.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        metric_name (str): Name of the performance metric
        metric_value (float): Value of the metric
        unit (str): Unit of measurement (ms, seconds, bytes, etc.)
        operation_type (str): Type of operation being measured
        metadata (dict): Additional metadata
        level (str): Log level
    """
    try:
        perf_metadata = metadata or {}
        perf_metadata.update(
            {
                "metric_name": metric_name,
                "metric_value": metric_value,
                "unit": unit,
            }
        )

        if operation_type:
            perf_metadata["operation_type"] = operation_type

        message = f"Performance metric: {metric_name} = {metric_value} {unit}"
        if operation_type:
            message += f" for {operation_type}"

        log_operation_info(
            operation_type="PERFORMANCE_METRIC",
            message=message,
            metadata=perf_metadata,
            level=level,
        )

    except Exception as e:
        error_log = json.dumps({"error": str(e)}, default=str)
        unique_id = get_current_unique_id()
        logger.error(f"ERROR | PERFORMANCE_LOGGING_FAILURE | {unique_id} | {error_log}")


def create_logging_context(request=None, user=None, operation_name=None):
    """
    Create a logging context with unique ID and common metadata.

    Args:
        unique_id (str): Unique identifier for correlation. This is mandatory.
        request: Django request object (optional)
        user: User object (optional)
        operation_name (str): Name of the operation (optional)

    Returns:
        dict: Logging context with unique_id and metadata

    Raises:
        ValueError: If unique_id is not provided.
    """
    try:

        context = {"metadata": {}}

        if operation_name:
            context["operation_name"] = operation_name

        if request:
            context["metadata"].update(
                {
                    "client_ip": get_client_ip(request),
                    "user_agent": (
                        request.META.get("HTTP_USER_AGENT", "")
                        if hasattr(request, "META")
                        else ""
                    ),
                    "request_method": getattr(request, "method", "UNKNOWN"),
                    "request_path": getattr(request, "path", ""),
                }
            )

        if user:
            context["metadata"].update(
                {
                    "user_id": getattr(user, "id", None),
                    "user_email": getattr(user, "email", None),
                    "user_role": getattr(user, "role", None),
                }
            )

        return context

    except Exception as e:
        # Fallback context if creation fails
        return {
            "metadata": {"error": str(e)},
        }
