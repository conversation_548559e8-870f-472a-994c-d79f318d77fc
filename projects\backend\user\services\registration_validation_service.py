"""
Registration Validation Service

This service handles all validation logic for user registration including:
- Rate limiting validation
- Device validation
- Duplicate user checks
- Input data validation
"""

from typing import Dict, Any, Tu<PERSON>, Optional
from django.utils import timezone
from oauth2_auth.utils import (
    get_client_ip,
    generate_device_fingerprint,
    get_dynamic_device_info,
)
from oauth2_auth.device_validation_service import device_validation_service
from oauth2_auth.rate_limiting_service import rate_limiting_service
from oauth2_auth.authentication import DeviceAuthenticationService
from agritram.exceptions import (
    ValidationException,
    DuplicateResourceException,
    raise_validation_error,
)
from agritram.logger_utils import (
    log_operation_info,
    log_security_event_standardized,
)
from user.models import User


class RegistrationValidationService:
    """Service for handling all registration validation logic"""

    @staticmethod
    def validate_rate_limiting(request, unique_id: str) -> None:
        """
        Validate rate limiting for registration attempts

        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Raises:
            ValidationException: If rate limit is exceeded
        """
        client_ip = get_client_ip(request)
        rate_check_id = f"{client_ip}_registration"

        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            unique_id=unique_id,
            identifier=rate_check_id,
            action="registration",
            request=request,
        )

        if not is_allowed:
            raise_validation_error(
                message="Too many registration attempts",
                details=rate_info.get("message", "Please try again later"),
                error_code="RATE_LIMITED",
            )

    @staticmethod
    def extract_and_validate_device_data(request, unique_id: str) -> Dict[str, Any]:
        """
        Extract and validate basic device data from request

        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Returns:
            Dict containing extracted device information

        Raises:
            ValidationException: If basic device data validation fails
        """
        import secrets

        # Extract registration data
        email = request.data.get("email", "").lower().strip()
        device_id = request.data.get("device_id")
        provided_device_type = request.data.get("device_type")
        user_provided_device_id = bool(device_id)

        # Generate device_id if not provided (for device tracking)
        if not device_id:
            # Use cryptographically secure random generation
            device_id = (
                f"{timezone.now().strftime('%Y%m%d%H%M%S')}_{secrets.token_urlsafe(32)}"
            )

        # Validate device_id format and length for security
        if not device_id or len(device_id) < 58:
            raise_validation_error(
                message="Invalid device identifier",
                details="Device ID must be at least 58 characters long",
            )

        # Get dynamic device information
        device_info = get_dynamic_device_info(request, "registration")
        device_name = device_info["device_name"]
        detected_device_type = device_info["device_type"]

        # Validate provided device_type if given
        if provided_device_type:
            if provided_device_type not in ["web", "mobile", "desktop", "api"]:
                raise_validation_error(
                    message="Invalid device type",
                    details="Device type must be one of: web, mobile, desktop, api",
                )
            device_type = provided_device_type
        else:
            device_type = detected_device_type

        # Log device type mismatch if detected
        if provided_device_type and provided_device_type != detected_device_type:
            log_operation_info(
                unique_id,
                "DEVICE_TYPE_MISMATCH",
                f"Device type mismatch during registration: provided={provided_device_type}, detected={detected_device_type}",
                metadata={
                    "email": email,
                    "device_id": device_id,
                    "provided_device_type": provided_device_type,
                    "detected_device_type": detected_device_type,
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                },
                level="WARNING",
            )

        return {
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
            "detected_device_type": detected_device_type,
            "provided_device_type": provided_device_type,
            "user_provided_device_id": user_provided_device_id,
            "email": email,
        }

    @staticmethod
    def validate_device_against_database(
        device_data: Dict[str, Any], request, unique_id: str
    ) -> None:
        """
        Validate device against existing devices in database when user provides device_id

        Args:
            device_data: Device information dictionary
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Raises:
            ValidationException: If device validation fails
        """
        # Only validate against DB if user explicitly provided device_id
        if device_data["user_provided_device_id"]:
            log_operation_info(
                unique_id,
                "DEVICE_DB_VALIDATION_START",
                f"Starting database validation for user-provided device: {device_data['device_id']}",
                metadata={
                    "device_id": device_data["device_id"],
                    "email": device_data["email"],
                    "device_type": device_data["device_type"],
                },
            )

            validation_result = (
                DeviceAuthenticationService.validate_device_for_registration(
                    device_id=device_data["device_id"],
                    device_type=device_data["device_type"],
                    request=request,
                    unique_id=unique_id,
                )
            )

            if not validation_result["is_valid"]:
                # Device validation failed - raise error
                log_operation_info(
                    unique_id,
                    "DEVICE_DB_VALIDATION_FAILED",
                    f"Database validation failed for device: {validation_result['message']}",
                    metadata={
                        "device_id": device_data["device_id"],
                        "email": device_data["email"],
                        "validation_message": validation_result["message"],
                    },
                    level="ERROR",
                )

                raise_validation_error(
                    message="Device validation failed",
                    details=validation_result["message"],
                )

            # Log any warnings from validation
            if validation_result["warnings"]:
                log_operation_info(
                    unique_id,
                    "DEVICE_DB_VALIDATION_WARNINGS",
                    f"Database validation warnings for registration: {', '.join(validation_result['warnings'])}",
                    metadata={
                        "email": device_data["email"],
                        "device_id": device_data["device_id"],
                        "warnings": validation_result["warnings"],
                        "validation_details": validation_result["details"],
                    },
                    level="WARNING",
                )

            log_operation_info(
                unique_id,
                "DEVICE_DB_VALIDATION_SUCCESS",
                f"Database validation successful for device: {device_data['device_id']}",
                metadata={
                    "device_id": device_data["device_id"],
                    "email": device_data["email"],
                    "validation_details": validation_result.get("details", {}),
                },
            )
        else:
            log_operation_info(
                unique_id,
                "DEVICE_DB_VALIDATION_SKIPPED",
                f"Skipping database validation for generated device: {device_data['device_id']}",
                metadata={
                    "device_id": device_data["device_id"],
                    "email": device_data["email"],
                    "reason": "device_id_generated_by_system",
                },
            )

        # Log comprehensive device validation summary
        log_operation_info(
            unique_id,
            "DEVICE_VALIDATION_COMPLETE",
            f"Device validation completed for registration: {device_data['device_id']}",
            metadata={
                "device_id": device_data["device_id"],
                "email": device_data["email"],
                "device_name": device_data["device_name"],
                "device_type": device_data["device_type"],
                "detected_device_type": device_data["detected_device_type"],
                "provided_device_type": device_data["provided_device_type"],
                "user_provided_device_id": device_data["user_provided_device_id"],
                "db_validation_performed": device_data["user_provided_device_id"],
                "validation_passed": True,
            },
        )

    @staticmethod
    def validate_duplicate_user(email: str, unique_id: str, request) -> None:
        """
        Check for duplicate registration attempts

        Args:
            email: User email to check
            unique_id: Unique request ID for correlation
            request: HTTP request object

        Raises:
            DuplicateResourceException: If user already exists
        """
        if email:
            try:
                existing_user = User.objects.get(email=email)
                if existing_user:
                    # Log security event with standardized logging
                    log_security_event_standardized(
                        unique_id,
                        "DUPLICATE_REGISTRATION_ATTEMPT",
                        "Registration attempt with existing active email",
                        user=existing_user,
                        request=request,
                        metadata={"email": email},
                        level="WARNING",
                    )
                    raise DuplicateResourceException(
                        message="User with this email already exists",
                        details="An active account with this email address is already registered",
                    )
            except User.DoesNotExist:
                pass

    @classmethod
    def validate_registration_request(cls, request, unique_id: str) -> Dict[str, Any]:
        """
        Perform complete validation of registration request

        Args:
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Returns:
            Dict containing validated data

        Raises:
            ValidationException: If any validation fails
            DuplicateResourceException: If user already exists
        """
        # Step 1: Validate rate limiting
        cls.validate_rate_limiting(request, unique_id)

        # Step 2: Extract and validate basic device data
        device_data = cls.extract_and_validate_device_data(request, unique_id)

        # Step 3: Validate device against database if user provided device_id
        cls.validate_device_against_database(device_data, request, unique_id)

        # Step 4: Check for duplicate users
        cls.validate_duplicate_user(device_data["email"], unique_id, request)

        return device_data
